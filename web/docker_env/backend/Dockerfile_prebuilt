# Dockerfile for pre-compiled web backend deployment
# Uses local compiled backend_deploy_* directory

FROM ubuntu:24.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV PYTHONIOENCODING=utf-8

# Configure apt proxy for faster downloads
RUN echo 'Acquire::http::Proxy "http://**********:10808/";' > /etc/apt/apt.conf.d/proxy

# Update system and install required packages
RUN apt-get update && apt-get install -y \
    tzdata \
    ca-certificates \
    locales \
    && locale-gen en_US.UTF-8 \
    && rm -rf /var/lib/apt/lists/*

# Set timezone
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create app directory
WORKDIR /backend

# Copy the pre-compiled backend deployment directory
# Note: The backend_deploy_* directory should be copied from host
COPY backend_deploy_*/ /backend/

# Ensure executables have proper permissions
RUN chmod +x /backend/backend_server /backend/start.sh /backend/health_check.sh

# Create logs directory with proper permissions
RUN mkdir -p /backend/logs && chmod 755 /backend/logs

# Create media directory with proper permissions
RUN mkdir -p /backend/media && chmod 755 /backend/media

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD /backend/health_check.sh

# Set entrypoint
ENTRYPOINT ["/backend/start.sh"]
