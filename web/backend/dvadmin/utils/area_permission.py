"""
属地权限控制工具类
"""
from django.db.models import Q
from dvadmin.system.models import Area


class AreaPermissionMixin:
    """属地权限控制混入类"""
    
    def get_user_area_codes(self, user):
        """获取用户可管理的所有属地代码（包括下级属地）"""
        if user.is_superuser:
            # 超级管理员可以管理所有属地
            return None
        
        user_area_code = getattr(user, 'user_area_id', None)
        if not user_area_code:
            # 用户没有设置属地，只能看到没有属地的数据
            return []
        
        # 获取用户属地及其所有下级属地
        return self._get_area_and_children(user_area_code)
    
    def _get_area_and_children(self, area_code):
        """递归获取属地及其所有下级属地"""
        area_codes = [area_code]
        
        # 获取直接下级
        children = Area.objects.filter(pcode_id=area_code).values_list('code', flat=True)
        
        # 递归获取所有下级
        for child_code in children:
            area_codes.extend(self._get_area_and_children(child_code))
        
        return area_codes
    
    def filter_by_area_permission(self, queryset, user, area_field='user_area'):
        """根据用户属地权限过滤查询集"""
        user_area_codes = self.get_user_area_codes(user)
        
        if user_area_codes is None:
            # 超级管理员，不过滤
            return queryset
        elif not user_area_codes:
            # 用户没有属地权限，只能看到没有属地的数据
            return queryset.filter(**{f'{area_field}__isnull': True})
        else:
            # 过滤用户可管理的属地数据
            return queryset.filter(**{f'{area_field}__in': user_area_codes})
    
    def filter_bras_by_area_permission(self, queryset, user):
        """根据用户属地权限过滤BRAS查询集"""
        return self.filter_by_area_permission(queryset, user, 'bras_area')
    
    def check_area_permission(self, user, target_area_code):
        """检查用户是否有权限管理指定属地的数据"""
        if user.is_superuser:
            return True
        
        user_area_codes = self.get_user_area_codes(user)
        if user_area_codes is None:
            return True
        
        return target_area_code in user_area_codes
    
    def get_area_choices_for_user(self, user):
        """获取用户可选择的属地选项"""
        if user.is_superuser:
            # 超级管理员可以选择所有属地
            return Area.objects.filter(enable=True).values('code', 'name', 'level').order_by('code')
        
        user_area_codes = self.get_user_area_codes(user)
        if not user_area_codes:
            return Area.objects.none()
        
        return Area.objects.filter(
            code__in=user_area_codes,
            enable=True
        ).values('code', 'name', 'level').order_by('code')


def get_area_hierarchy(area_code=None):
    """获取属地层级结构"""
    if area_code:
        # 获取指定属地的层级结构
        areas = Area.objects.filter(
            Q(code=area_code) | Q(pcode_id=area_code)
        ).order_by('level', 'code')
    else:
        # 获取所有属地的层级结构
        areas = Area.objects.filter(enable=True).order_by('level', 'code')
    
    # 构建层级结构
    area_dict = {}
    root_areas = []
    
    for area in areas:
        area_dict[area.code] = {
            'code': area.code,
            'name': area.name,
            'level': area.level,
            'pcode': area.pcode_id,
            'children': []
        }
    
    # 构建父子关系
    for area in areas:
        if area.pcode_id and area.pcode_id in area_dict:
            area_dict[area.pcode_id]['children'].append(area_dict[area.code])
        else:
            root_areas.append(area_dict[area.code])
    
    return root_areas


def validate_area_permission(user, target_area_code, operation='view'):
    """验证用户对指定属地的操作权限"""
    if user.is_superuser:
        return True, "超级管理员拥有所有权限"
    
    if not target_area_code:
        return True, "无属地限制"
    
    user_area_code = getattr(user, 'user_area_id', None)
    if not user_area_code:
        return False, "用户未设置属地，无权限操作"
    
    # 检查目标属地是否在用户管理范围内
    mixin = AreaPermissionMixin()
    user_area_codes = mixin.get_user_area_codes(user)
    
    if target_area_code not in user_area_codes:
        return False, f"无权限操作属地 {target_area_code} 的数据"
    
    return True, "权限验证通过"


def get_area_name_by_code(area_code):
    """根据属地代码获取属地名称"""
    if not area_code:
        return ""

    try:
        area = Area.objects.get(code=area_code)
        return area.name
    except Area.DoesNotExist:
        return area_code  # 如果找不到，返回代码本身


def get_area_and_children_codes(area_code):
    """获取指定属地及其所有下级属地的代码列表"""
    if not area_code:
        return []

    mixin = AreaPermissionMixin()
    return mixin._get_area_and_children(area_code)
