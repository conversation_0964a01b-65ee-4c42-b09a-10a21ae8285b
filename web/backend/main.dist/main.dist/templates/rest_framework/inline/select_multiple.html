{% load i18n %}
{% load rest_framework %}
{% trans "No items to select." as no_items %}

<div class="form-group {% if field.errors %}has-error{% endif %}">
  {% if field.label %}
    <label class="sr-only">
      {{ field.label }}
    </label>
  {% endif %}

  <select multiple {{ field.choices|yesno:",disabled" }} class="form-control" name="{{ field.name }}">
      {% for select in field.iter_options %}
          {% if select.start_option_group %}
            <optgroup label="{{ select.label }}">
          {% elif select.end_option_group %}
            </optgroup>
          {% else %}
            <option value="{{ select.value }}" {% if select.value|as_string in field.value|as_list_of_strings %}selected{% endif %} {% if select.disabled %}disabled{% endif %}>{{ select.display_text }}</option>
          {% endif %}
      {% empty %}
      <option>{{ no_items }}</option>
    {% endfor %}
  </select>
</div>
