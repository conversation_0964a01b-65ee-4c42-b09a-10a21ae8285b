# Dockerfile for web backend deployment with system-provided Python dependencies
# <PERSON>uitka compiles app code, system provides frameworks like Django, DRF etc.

FROM ubuntu:24.04

ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV PYTHONIOENCODING=utf-8

RUN apt-get update && apt-get install -y --no-install-recommends \
    tzdata \
    ca-certificates \
    locales \
    curl \
    python3 \
    # python3-pip # 可能需要，如果有些包apt没有或版本不对，需要用pip补充

    # Install Python packages that Nuitka was told to --nofollow-import-to
    # 确保这些包名在Ubuntu 24.04的apt源中是正确的
    python3-django \
    python3-djangorestframework \
    python3-django-corsheaders \
    # 检查 'python3-django-simple-captcha' 或 'python3-django-captcha' 的确切名称
    # 如果apt没有，可能需要用pip安装，例如：
    # python3-pip \
    # RUN pip3 install django-simple-captcha
    python3-channels \
    python3-uvicorn \
    python3-gunicorn \
    python3-openpyxl \
    python3-requests \
    python3-six \
    python3-whitenoise \
    python3-gevent \
    python3-celery \
    python3-pandas \
    python3-mysqlclient \ # 这个包通常会带上它依赖的系统C库 (libmysqlclient-dev or libmysqlclient21)

    # 如果上述apt包没有自动安装其C语言依赖，或者某些包是通过pip安装的，
    # 你仍需确保其C库被安装。
    # 例如，python3-mysqlclient通常会依赖并安装libmysqlclient21。
    # 如果某些包用pip安装，如 'pip install psycopg2-binary'，它会自带C库，
    # 但如果是 'pip install psycopg2'，则需要手动安装 libpq-dev 和其他编译依赖，然后是 libpq5 运行时库。

    # 对于通过apt安装的python3-mysqlclient，它应该已经处理了libmysqlclient21的依赖。
    # 如果你发现仍缺少，可以显式添加：
    # libmysqlclient21

    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 如果某些包必须通过pip安装 (因为apt没有或版本不满足要求)
# COPY requirements_docker_pip.txt .
# RUN pip3 install --no-cache-dir -r requirements_docker_pip.txt

RUN locale-gen en_US.UTF-8
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

WORKDIR /backend
COPY backend_deploy_*/ /backend/

RUN chmod +x /backend/start.sh /backend/health_check.sh
RUN if [ -f /backend/app_dist/backend_server ]; then chmod +x /backend/app_dist/backend_server; fi

RUN mkdir -p /backend/logs && chmod 777 /backend/logs
RUN mkdir -p /backend/media && chmod 777 /backend/media

EXPOSE 8000
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /backend/health_check.sh
ENTRYPOINT ["/backend/start.sh"]