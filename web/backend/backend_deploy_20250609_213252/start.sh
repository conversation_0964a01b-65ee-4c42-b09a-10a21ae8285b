#!/bin/bash
# Web Backend服务器启动脚本

# 检查配置文件
if [[ ! -f "conf/env.py" ]]; then
    echo "错误: 配置文件 conf/env.py 不存在"
    exit 1
fi

# 启动服务器
echo "启动Web Backend服务器..."
# Execute the program from its location within app_dist
if [[ -f "./app_dist/main.dist/backend_server" ]]; then
    ./app_dist/main.dist/backend_server
elif [[ -f "./app_dist/backend_server" ]]; then
    ./app_dist/backend_server
else
    echo "错误: 找不到 backend_server 可执行文件"
    exit 1
fi
