[project]
name = "vpdn-management"
version = "0.1.0"
description = "VPDN Management System"
# It's good practice to specify the Python version
requires-python = ">=3.12" # Or ">=3.11" if that's your minimum, adjust as needed

# You should eventually move all dependencies from requirements.txt here
# For now, just keeping what you have.
dependencies = [
    "celery==5.4.0",
    "channels==4.1.0",
    "channels-redis==4.2.0",
    "django==4.2.14",
    "django-comment-migrate==0.1.7",
    "django-cors-headers==4.4.0",
    "django-filter==24.2",
    "django-ranged-response==0.2.0",
    "django-restql==0.15.4",
    "django-simple-captcha==0.6.0",
    "django-timezone-field==7.0",
    "djangorestframework==3.15.2",
    "djangorestframework-simplejwt==5.3.1",
    "drf-yasg==1.21.7",
    "gevent==24.2.1",
    "gunicorn==22.0.0",
    "mysqlclient==2.2.0",
    "openpyxl==3.1.5",
    "pandas==2.3.0",
    "pillow==10.4.0",
    "pyinstaller==6.9.0",
    "pyparsing==3.1.2",
    "pypinyin==0.51.0",
    "requests==2.32.3",
    # Add other direct dependencies here.
    # For example, Django, djangorestframework, etc., from your requirements.txt
    # "django~=4.2", # Example
    # "djangorestframework~=3.14", # Example
    "six==1.16.0",
    "typing-extensions==4.12.2",
    "tzlocal==5.2",
    "ua-parser==0.18.0",
    "user-agents==2.2.0",
    "uvicorn==0.30.3",
    "websockets==11.0.3",
    "whitenoise==6.7.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
# List your top-level Python package directories here.
# Hatch will automatically find .py files and sub-packages within these.
packages = [
    "application",
    "dvadmin",
    "plugins",
    "conf" # Include this if you import from it, e.g., `from conf import env`
]

# If you have scripts that should be installed (like manage.py, but that's usually not "installed")
# you would define them under [project.scripts]
# e.g.
# [project.scripts]
# my-script = "application.cli:main"

# If you have top-level .py files in 'backend/' that are meant to be modules
# (e.g., if you would `import main` or `import gunicorn_conf` from other code),
# and they are not part of the packages listed above, you might need to add:
# include = ["main.py", "gunicorn_conf.py"]
# However, for a Django project, 'manage.py' is an entry script and not typically
# part of the "installed" package modules. 'main.py' could be an entry for ASGI/WSGI.
