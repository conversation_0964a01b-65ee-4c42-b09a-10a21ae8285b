# Dockerfile for pre-compiled radius server deployment
# Uses local compiled radius_deploy_* directory

FROM ubuntu:24.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV PYTHONIOENCODING=utf-8

# Configure apt proxy for faster downloads
RUN echo 'Acquire::http::Proxy "http://**********:10808/";' > /etc/apt/apt.conf.d/proxy

# Update system and install required packages
RUN apt-get update && apt-get install -y \
    tzdata \
    ca-certificates \
    locales \
    && locale-gen en_US.UTF-8 \
    && rm -rf /var/lib/apt/lists/*

# Set timezone
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create app directory
WORKDIR /app

# Copy the pre-compiled radius deployment directory
# Note: The radius_deploy_* directory should be copied from host
COPY radius_deploy_*/ /app/

# Ensure executables have proper permissions
RUN chmod +x /app/radius_server /app/start.sh /app/health_check.sh

# Create logs directory
RUN mkdir -p /app/logs

# Expose ports
EXPOSE 1812/udp 1813/udp 3799/udp

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD /app/health_check.sh

# Set entrypoint
ENTRYPOINT ["/app/start.sh"]
