# 日志配置说明

## 概述

系统使用基于配置文件的多日志系统，所有日志配置都在 `conf/logging.conf` 中统一管理。

## 日志文件类型

### 1. system.log
- **用途**: 记录系统级错误和异常
- **轮转**: 每天一个文件 (midnight)
- **保留**: 30天
- **级别**: ERROR及以上
- **格式**: `[时间戳] [级别] 消息`

### 2. authentication.log
- **用途**: 记录用户认证相关日志
- **轮转**: 每小时一个文件 (H)
- **保留**: 168小时 (7天)
- **级别**: INFO及以上
- **格式**: `[时间戳.毫秒] [级别] [请求ID][用户名][客户端IP] 消息`

### 3. accounting.log
- **用途**: 记录用户计费相关日志
- **轮转**: 每小时一个文件 (H)
- **保留**: 168小时 (7天)
- **级别**: INFO及以上
- **格式**: `[时间戳.毫秒] [级别] [请求ID][用户名][客户端IP] 消息`

### 4. coa.log
- **用途**: 记录COA (Change of Authorization) 相关日志
- **轮转**: 每小时一个文件 (H)
- **保留**: 168小时 (7天)
- **级别**: INFO及以上
- **格式**: `[时间戳.毫秒] [级别] [请求ID][用户名][客户端IP] 消息`

## 配置文件结构

### 日志器 (Loggers)
- `system`: 系统错误日志器
- `authentication`: 认证日志器
- `accounting`: 计费日志器
- `coa`: COA日志器

### 处理器 (Handlers)
- `systemHandler`: 系统日志文件处理器
- `authHandler`: 认证日志文件处理器
- `acctHandler`: 计费日志文件处理器
- `coaHandler`: COA日志文件处理器

### 格式化器 (Formatters)
- `simpleFormatter`: 简单格式 (用于系统日志)
- `contextFormatter`: 上下文格式 (用于业务日志，包含请求ID、用户名、客户端IP)

## 修改配置

要修改日志配置，请编辑 `conf/logging.conf` 文件：

1. **修改日志级别**: 在对应的 `[logger_xxx]` 部分修改 `level` 参数
2. **修改轮转策略**: 在对应的 `[handler_xxx]` 部分修改 `args` 参数
3. **修改保留时间**: 修改 `args` 参数中的最后一个数字
4. **修改日志格式**: 在对应的 `[formatter_xxx]` 部分修改 `format` 参数

## 轮转参数说明

TimedRotatingFileHandler 的 `args` 参数格式：
```
args=('日志文件路径', '轮转间隔类型', 间隔数量, 保留文件数量)
```

轮转间隔类型：
- `'S'`: 秒
- `'M'`: 分钟
- `'H'`: 小时
- `'D'`: 天
- `'midnight'`: 每天午夜

## 使用示例

在代码中使用日志：

```python
from aaaserver import multi_logger

# 开始请求追踪
req_id = multi_logger.start_request("*************", "username", "auth")

# 记录认证日志
multi_logger.auth_info("用户认证成功")
multi_logger.auth_error("用户认证失败")

# 记录计费日志
multi_logger.acct_info("计费开始")

# 记录COA日志
multi_logger.coa_info("COA请求处理")

# 记录系统错误
multi_logger.system_error("系统异常", exc_info=True)

# 结束请求追踪
multi_logger.end_request("success")
```
