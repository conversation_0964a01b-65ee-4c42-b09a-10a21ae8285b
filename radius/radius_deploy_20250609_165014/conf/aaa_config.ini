[authentication]
# 认证开关配置
# 1 = 启用认证, 0 = 禁用认证（仅记录日志）

# 用户名验证开关
username_check_enabled = 1

# 密码验证开关
password_check_enabled = 0

# 用户状态验证开关
user_status_check_enabled = 1

# 在线数量限制验证开关
online_limit_check_enabled = 0

# 域名存在性验证开关
domain_check_enabled = 1

[authorization]
# 授权配置

# Session-Timeout (秒) - 会话超时时间
session_timeout = 172800

# Acct-Interim-Interval (秒) - 计费中间间隔
acct_interim_interval = 7200

# Framed-IP-Netmask - 帧IP网络掩码
framed_ip_netmask = ***************

# Service-Type - 服务类型 (2=Framed)
service_type = 2

# Framed-Protocol - 帧协议 (1=PPP)
framed_protocol = 1

[performance]
# 性能配置

# 最大并发协程数量
max_concurrent_tasks = 500

# 单个任务超时时间（秒）
task_timeout_seconds = 30.0

# 统计报告间隔（秒）
stats_interval = 60

[coa]
# CoA (Change of Authorization) 服务器配置

# CoA服务器启用开关
coa_enabled = 1

# CoA服务器监听端口
coa_port = 3799

# CoA请求处理超时时间（秒）
coa_timeout_seconds = 10.0

# CoA操作类型配置
# 支持的操作：disconnect, coa_request
supported_operations = disconnect,coa_request

# CoA响应配置
# 默认响应代码：CoA-ACK(44), CoA-NAK(45), Disconnect-ACK(40), Disconnect-NAK(41)
default_coa_response = nak
default_disconnect_response = nak
